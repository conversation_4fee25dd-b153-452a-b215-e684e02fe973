import { configDefaults } from 'vitest/config';
import { defineVitestConfig } from '@nuxt/test-utils/config';
import vue from '@vitejs/plugin-vue';
import tailwindcss from '@tailwindcss/vite';
import AutoImport from 'unplugin-auto-import/vite';

export default defineVitestConfig({
  test: {
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './tests/coverage',
      exclude: [
        ...configDefaults.coverage?.exclude || [],
        '**/*.cy.ts',
        '**/*.cy.tsx',
        '**/cypress/**',
        'nuxt.config.ts'
      ],
      thresholds: {
        statements: 90,
        branches: 90,
        functions: 90,
        lines: 90,
      },
    },
    projects: [
      {
        plugins: [
          vue(),
          AutoImport({
            imports: ['vue'],
            dts: true,
          }),
        ],
        test: {
          include: [
            'tests/server/**/*.{test,spec}.ts',
          ],
          name: 'Server (Node Environment)',
          globals: true,
          environment: 'node',
        },
      },
      {
        plugins: [
          vue(),
          tailwindcss(),
          AutoImport({
            imports: ['vue'],
            dts: true,
          }),
        ],
        test: {
          include: [
            'tests/**/*.{test,spec}.ts',
          ],
          exclude: [
            'tests/server/*.{test,spec}.ts',
          ],
          name: 'Happy DOM Environment',
          globals: true,
          environment: 'happy-dom',
        },
      },
    ],
  },
})


