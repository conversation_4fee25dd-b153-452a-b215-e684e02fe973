version: 1
frontend:
  phases:
    preBuild:
      commands:
        - nvm install 22
        - nvm use 22
        - node -v
        - npm -v
        - rm -rf node_modules
        - npm install --cache .npm --prefer-offline
    build:
      commands:
        - npm run build
    postBuild:
      commands:
        - echo "✅ Build completed"
  artifacts:
    baseDirectory: .output/public
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
test:
  phases:
    preTest:
      commands:
        - echo "🧪 Running tests with coverage"
    test:
      commands:
        - npm run test:coverage
        - npm run test:e2e
    postTest:
      commands:
        - mkdir -p test-artifacts/coverage
        - mkdir -p test-artifacts/screenshots
        - mkdir -p test-artifacts/videos
        - cp -r tests/coverage/* test-artifacts/coverage/
        - cp -r cypress/screenshots/* test-artifacts/screenshots/ || echo "no screenshots"
        - cp -r cypress/videos/* test-artifacts/videos/ || echo "no videos"
        - echo "✅ Test completed"
  artifacts:
    baseDirectory: test-artifacts
    files:
      - '**/*'
