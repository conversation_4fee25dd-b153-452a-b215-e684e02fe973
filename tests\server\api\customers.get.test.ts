import { describe, it, expect, vi } from 'vitest';
import h3EventMock from '../../mocks/h3EventMock';

vi.mock('../../../server/utils/handler', () => ({
  defineWrappedResponseHandler: (fn: any) => fn,
}))

const expectedData = [{ id: '123', name: '<PERSON>' }];

vi.mock('../../../server/utils/supabase.ts', () => ({
  supabase: () => ({
    from: () => ({
      select: () => {
        return {
          data: expectedData,
          success: true,
          error: null,
        };
      },
    }),
  }),
}));

vi.mock('../../../server/utils/runtime.ts', () => ({
  getConfig: () => ({
    supabaseUrl: 'https://mock.supabase.co',
    supabaseKey: 'mock-secret-key',
  }),
}))

import handler from '../../../server/api/customers.get'

describe('GET /api/customers', () => {
  beforeEach(() => {
    vi.resetModules();
    vi.clearAllMocks();
  })

  it('returns customer data', async () => {
    const response = await handler(h3EventMock)

    expect(response).toEqual(expectedData)
  })

  it('handles Supabase errors gracefully', async () => {
    const error = 'Supabase error'
    vi.doMock('../../../server/utils/supabase.ts', () => ({
      supabase: () => ({
        from: () => ({
          select: () => ({
            data: null,
            error: { message: error },
          }),
        }),
      }),
    }))

    const errorHandler = (await import('../../../server/api/customers.get')).default;
    await expect(errorHandler(h3EventMock)).rejects.toThrow(error);
  })
})