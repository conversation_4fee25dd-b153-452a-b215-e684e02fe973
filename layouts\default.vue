<script setup lang="ts"></script>

<template>
  <div name="layout-container" class="flex flex-col h-screen">
    <header data-test="layout-header" class="bg-base-200  shadow">
      <slot name="header"></slot>
    </header>

    <div  name="body-container" class="flex flex-1 overflow-hidden pt-4">
      <aside data-test="sidebar-container" class="w-60">
        <slot name="sidebar"></slot>
      </aside>

      <main data-test="main-container" name="main-container" class="flex-1 p-4 sm:p-6 overflow-y-auto">
        <slot></slot>
      </main>
    </div>
  </div>
</template>

<style scoped></style>
