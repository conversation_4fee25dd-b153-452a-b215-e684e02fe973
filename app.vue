<script setup>
  import But<PERSON> from './components/Button.vue';

  const customers = ref([])
  const error = ref(null)
  const pending = ref(true)

  onMounted(async () => {
    try {
      const response = await $fetch('/api/customers')
      customers.value = response.data || []
    } catch (err) {
      error.value = err
    } finally {
      pending.value = false
    }
  })
</script>

<template>
  <NuxtLayout>
    <template v-slot:header>
      <Header/>
    </template>
    <template v-slot:sidebar>
      <Sidebar/>
    </template>
    <NuxtPage />
    <!-- Example of data usage -->
    <div>
      <h1>Customers - Example if data usage</h1>
      <p>Extra content from Birgit - test</p>
      <p v-if="pending">Loading...</p>
      <p v-else-if="error">Error loading customers: {{ error.message }}</p>
      <ul v-if="customers">
        <li v-for="c in customers" :key="c.id">
          {{ c.name }} ({{ c.email }})
        </li>
      </ul>
    </div>
    <!-- Example of data usage end -->
    <Button />
  </NuxtLayout>
</template>
