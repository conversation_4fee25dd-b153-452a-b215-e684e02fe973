# Oracom Platform

The Platform where you can set up your b

## Setup

```bash
npm install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
npm run dev
```

## Unit test

Is done with Vitest. If you have some functionality to test, use Vitest.

```bash
npm run test # run tests
npm run test:watch # watch test changes
npm run test:changed # run changed tests only
npm run test:coverage # run coverage report /tests/coverage as out directory
npm run test:ui # Run Vitest UI, to see how you cover the lines
```

### Mocking strategies

Unit tests require the ability to mock things and spy on stuff. So that a unit to be tested is really the only unit to be tested and not the entire integration. Therefore we decouple the units with mocks.

[What are Mocks, Stubs and Spies](https://spin.atomicobject.com/mocks-stubs-or-spies/)?

- [Nuxt Component mocking](https://nuxt.com/docs/getting-started/testing#%EF%B8%8F-helpers)
- [Vitest mocks and spies](https://vitest.dev/api/mock.html)
[Cypress mock requests](https://docs.cypress.io/app/guides/network-requests)
- [Cypress Stubs, Spes and Clocks](https://docs.cypress.io/app/guides/stubs-spies-and-clocks)

## E2E and Component Test

Ende-to-end tests integrate literally every unit and component. Nothing is mocked. But recently Cypress has the ability to test single components as well.

This is more visual. We end-to-end flows and also single components.

```bash
npm run dev # you need to have the frontend running first
npm run cy:open # opens Cypress dashboard
# or
npm run cy:cli # to run in terminal
```

Wite tests under `./cypress/e2e/` for end-to-end tests and `./cypress/components/` for component tests.

### E2E tests

E2E tests are user flows

```txt
FLOW: Creating a New Agent

When I log in, I land on the dashboard.
The dashboard serves as the home page, inviting me to create a bot.
I can choose from different types of bots, such as:
- WhatsApp chatbot
- Voice Emma (as a Voice SmartGuide)
- etc.
I select one of them (for example, Voice Emma).
As a non-technical user, I now see an interface that allows me to easily configure the bot according to my needs.
- General: Name, greeting, behavior, voice
- Knowledge management (as a prompt in text form, from a website, data import (PMT as a data carrier), more options later...)
Once I have finished setting it up, I confirm the settings and generate the bot.
The bot is now available for me to test and make any necessary adjustments.
Once I am satisfied, I want to integrate the bot into my infrastructure, i.e., my website. For this, I find several options:
- Integration via iFrame
- Connection via JavaScript code
Back on the dashboard, I can always find the agent and make adjustments later.
```

Here is an example of a flow inside a [Github item](https://github.com/Oracommit/knowledge-transfer/issues/12#issue-2931284311), that you can use as an inspiration.

## Production

Build the application for production:

```bash
npm run build
```

Locally preview production build:

```bash
npm run preview
```

## Tools

- [Nuxt](https://nuxt.com/docs/getting-started/introduction) - Our frontend framework.
- [Deployment documentation](https://nuxt.com/docs/getting-started/deployment) for production.
- [Tailwind](https://tailwindcss.com/docs/installation/using-vite) - Quickly add CSS
- [DaisyUI](https://daisyui.com/docs/install/nuxt/) - Component library on top of TailwindCSS
- [Vitest](https://vitest.dev/guide/) - Test Unit test
- [Cypress E2E](https://docs.cypress.io/app/end-to-end-testing/writing-your-first-end-to-end-test) - Wnd-to-end test
- [Cypress Component](https://docs.cypress.io/app/component-testing/get-started) - Component test
- [Pre-Hook Husky](https://typicode.github.io/husky/)
