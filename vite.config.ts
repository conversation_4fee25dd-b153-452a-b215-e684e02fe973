// Created for Cypress testing
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';
import tailwindcss from '@tailwindcss/vite';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue(), tailwindcss()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './'),
      '~': path.resolve(__dirname, './'),
    },
  },
  server: {
    port: 3000,
    strictPort: true,
    open: true,
    cors: true,
  },
  test: {
    globals: true,
    environment: 'node',
  },
});
