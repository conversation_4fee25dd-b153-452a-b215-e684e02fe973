import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import ErrorComponent from '../error.vue'
import type { H3Error } from 'h3'

// NuxtLink needs to be stubbed
const NuxtLinkStub = {
  template: '<a><slot /></a>'
}

describe('error.vue', () => {
  it('renders the status code and fallback link', () => {
    const wrapper = mount(ErrorComponent, {
      props: {
        error: {
          statusCode: 418, // I'm a teapot ☕
          statusMessage: "Short and stout",
          fatal: false,
          unhandled: false,
          toJSON: function (): Pick<H3Error<unknown>, 'data' | 'statusCode' | 'statusMessage' | 'message'> {
            throw new Error('Function not implemented.')
          },
          name: '',
          message: ''
        }
      },
      global: {
        stubs: {
          NuxtLink: NuxtLinkStub
        }
      }
    })

    expect(wrapper.text()).toContain('Ein Fehler ist passiert.')
    expect(wrapper.text()).toContain('418')
    expect(wrapper.find('a').exists()).toBe(true)
  })
})
