@import "tailwindcss";
@plugin  "daisyui" {
  logs: false;
  themes: ["OracomTheme"];
}

@plugin "daisyui/theme" {
  name: "OracomTheme";
  default: true; /* set as default */
  prefersdark: false; /* set as default dark mode (prefers-color-scheme:dark) */
  color-scheme: light; /* color of browser-provided UI */

  --color-base-100: oklch(100% 0 0);/*oracom white*/
  --color-base-200: oklch(95% 0.03 240);
  --color-base-300: oklch(92% 0.04 240);
  --color-base-content: oklch(20% 0.05 240);
  --color-primary: oklch(48.58% 0.19 276.14);/*oracom blue*/
  --color-primary-content: oklch(98% 0.01 240);
  --color-secondary: oklch(77.45% 0.21 157.54);/*oracom green*/
  --color-secondary-content: oklch(72.51% 0.19 160.88);/*oracom light green*/
  --color-accent: oklch(65% 0.25 160);
  --color-accent-content: oklch(98% 0.01 160);
  --color-neutral: oklch(29.8% 0.015 277.4);/*oracom gray*/
  --color-neutral-content: oklch(97.32% 0.01 236.21);/*oracom light gray*/
  --color-info: oklch(70% 0.2 220);
  --color-info-content: oklch(98% 0.01 220);
  --color-success: oklch(65% 0.25 140);
  --color-success-content: oklch(98% 0.01 140);
  --color-warning: oklch(80% 0.25 80);
  --color-warning-content: oklch(20% 0.05 80);
  --color-error: oklch(65% 0.3 30);
  --color-error-content: oklch(98% 0.01 30);

  /* border radius */
  --radius-selector: 0rem;
  --radius-field: 0rem;
  --radius-box: 0rem;

  /* base sizes */
  --size-selector: 0.25rem;
  --size-field: 0.25rem;

  /* border size */
  --border: 1px;

  /* effects */
  --depth: 1;
  --noise: 0;
}
.oracom-shadow {
  box-shadow: 5px 5px 0 0 var(--color-secondary-content);
}