import type { EventHandler, EventHandlerRequest } from 'h3'

type ApiResponse<D> =
  | { success: true; data: D }
  | { success: false; error: { message: string; stack?: string } }

export const defineWrappedResponseHandler = <T extends EventHandlerRequest, D>(
  handler: EventHandler<T, D>
): EventHandler<T, ApiResponse<D>> =>
  defineEventHandler<T>(async event => {
    try {
      const response = await handler(event)

      return { success: true, data: response }
    } catch (err: any) {
      console.error('API Error:', err.message)
      return {
        success: false,
        error: {
          message: err.message || 'Unknown error',
          stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
        },
      }
    }
  })
