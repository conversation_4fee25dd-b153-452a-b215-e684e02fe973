<script setup lang="ts">
import { defineProps } from "vue";

const props = defineProps<{
  text: string;
  icon: string;
  active: boolean;
}>();
</script>

<template>
  <div class="relative">
    <button
      :class="[
        'w-full flex items-center justify-between px-4 py-2 font-semibold cursor-pointer',
        active
          ? 'bg-primary text-base-100'
          : 'text-neutral-content hover:text-primary brightness-50',
      ]"
    >
      <div class="flex items-center gap-2">
        <img
          :src="props.icon"
          alt="icon"
          class="h-5 w-5"
          :class="props.active ? 'brightness-100' : 'brightness-100'"
        />
        {{ props.text }}
      </div>
      <div
        v-if="props.active"
        class="absolute bottom-[-3px] left-[2px] w-[100%] h-[3px] bg-secondary-content"
      />
      <div
        v-if="props.active"
        class="absolute top-[3px] right-[-3px] h-[100%] w-[3px] bg-secondary-content"
      />
    </button>
  </div>
</template>

<style scoped></style>
