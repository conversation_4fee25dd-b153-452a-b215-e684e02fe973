import { createClient, type SupabaseClient } from '@supabase/supabase-js'
import { getConfig } from './runtime'
import type { Database } from '../../types/database'

let _supabase: SupabaseClient<Database> | null = null

export function supabase(): SupabaseClient<Database> {
  if (_supabase) return _supabase

  const config = getConfig()
  _supabase = createClient<Database>(
    config.supabaseUrl,
    config.supabaseKey
  )

  return _supabase
}
/** Resets the singleton */
export function resetSupabaseClient() {
  _supabase = null;
}