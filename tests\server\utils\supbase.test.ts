import { describe, it, expect, vi, beforeEach } from 'vitest';

const { createClientMock } = vi.hoisted(() => {
  return {
    createClientMock: vi.fn(() => ({
      from: vi.fn(),
    })),
  };
});

vi.mock('@supabase/supabase-js', () => ({
  createClient: createClientMock,
}));

vi.mock('../../../server/utils/runtime', () => ({
  getConfig: () => ({
    supabaseUrl: 'https://mock.supabase.co',
    supabaseKey: 'mock-secret-key',
  }),
}));

import { resetSupabaseClient, supabase } from '../../../server/utils/supabase';
import { createClient } from '@supabase/supabase-js';

describe('supabase()', () => {
  beforeEach(() => {
    // vi.resetModules();
    resetSupabaseClient();
    vi.clearAllMocks();
  });

  it('should create a Supabase client with config from useRuntimeConfig()', () => {
    const client = supabase()

    expect(createClient).toHaveBeenCalledOnce()
    expect(createClient).toHaveBeenCalledWith(
      'https://mock.supabase.co',
      'mock-secret-key'
    )

    expect(client).toBeDefined()
    expect(typeof client.from).toBe('function')
  });

  it('returns existing Supabase client if already initialized', () => {
    const client1 = supabase();
    const client2 = supabase();

    expect(client1).toBe(client2);
    expect(createClientMock).toHaveBeenCalledTimes(1);
  });
});
