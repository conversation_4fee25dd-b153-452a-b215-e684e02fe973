import { describe, it, expect, vi, beforeEach } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils'
import App from '../app.vue';



describe('app.vue', () => {
  beforeEach(() => {
    vi.resetModules();
    vi.clearAllMocks();
  });

  it('renders customer list correctly', async () => {
    vi.stubGlobal('$fetch', () => Promise.resolve({
      data: [
        { id: '1', name: '<PERSON>', email: '<EMAIL>' },
        { id: '2', name: '<PERSON>', email: '<EMAIL>' },
      ],
    }));

    const wrapper = mount(App)
    await flushPromises();

    expect(wrapper.text()).toContain('Customers - Example if data usage');
    expect(wrapper.text()).toContain('Alice (<EMAIL>)');
    expect(wrapper.text()).toContain('Bob (<EMAIL>)');
  });

  it('handles undefined response data by setting customers to an empty array', async () => {
    vi.stubGlobal('$fetch', () => Promise.resolve({})); // response.data is undefined

    const wrapper = mount(App);
    await flushPromises();

    // Check that the customers list is empty
    const listItems = wrapper.findAll('ul li');
    expect(listItems.length).toBe(0);

    // Optionally, check that the loading and error messages are not displayed
    expect(wrapper.text()).not.toContain('Loading...');
    expect(wrapper.text()).not.toContain('Error loading customers:');
  });

  it('renders loading message while fetching data', async () => {
    let resolveFetch: ((value: unknown) => void) | undefined;
    vi.stubGlobal('$fetch', () =>
      new Promise((resolve) => {
        resolveFetch = resolve;
      })
    );

    const wrapper = mount(App);
    expect(wrapper.text()).toContain('Loading...');

    if (resolveFetch) {
      resolveFetch({ data: [] });
    }
    await flushPromises();

    expect(wrapper.text()).not.toContain('Loading...');
  });

  it('renders error message when fetch fails', async () => {
    const mockError = new Error('Fetch failed');
    vi.stubGlobal('$fetch', () => Promise.reject(mockError));

    const wrapper = mount(App);
    await flushPromises();

    expect(wrapper.text()).toContain('Error loading customers: Fetch failed');
  });
});
