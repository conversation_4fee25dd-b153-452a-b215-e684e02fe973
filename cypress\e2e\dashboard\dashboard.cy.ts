// Describe a user story for the dashboard page
// Example:
// As a user, I want to see a button on the dashboard that redirects me to another page when clicked.
// Then I want to add a new bot, therefore I click on adding a new bot button.
// etc.
// ***********************************************************

describe('Dashboard', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('should display a button', () => {
    cy.get('.btn').should('contain', 'Click me!');
  });

  it('should go to andereSeite if button clicked', () => {
    cy.wait(1000);
    cy.get('[data-cy="myButton"]')
      .should('be.visible')
      .should('not.be.disabled')
      .click();
    cy.url().should('include', '/andereSeite');
    cy.contains('andereSeite').should('exist');
  });
});