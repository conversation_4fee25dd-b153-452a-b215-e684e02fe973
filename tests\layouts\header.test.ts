import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import Header from '../../components/Header.vue'

describe('Header.vue', () => {
  const wrapper = mount(Header)
  it('renders logo', () => {
    const logo = wrapper.get('img[alt="Logo"]')
    expect(logo.attributes('src')).toBe('/oracom-intelligence-logo.svg')
  })
  it('renders user avatar', () => {
    const avatar = wrapper.get('img[alt="User Avatar"]')
    expect(avatar.attributes('src')).toBe('/user-avatar.svg')
  })
})
