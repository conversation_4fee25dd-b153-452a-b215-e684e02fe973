import { mount } from "@vue/test-utils";
import { describe, it, expect, vi } from "vitest";
import Sidebar from "../../components/Sidebar.vue";
import SidebarButton from "../../components/SidebarButton.vue";

vi.mock("@/components/SidebarButton.vue", () => ({
  default: {
    name: "SidebarButton",
    props: ["text", "icon", "active"],
    emits: ["click"],
    template: `<button @click="$emit('click')">{{ text }}</button>`,
  },
}));

describe("Sidebar.vue", () => {
  it("renders all buttons", () => {
    const wrapper = mount(Sidebar);
    const buttons = wrapper.findAllComponents(SidebarButton);
    expect(buttons).toHaveLength(2);
    expect(buttons[0].text()).toContain("Bot-Dashboard");
    expect(buttons[1].text()).toContain("Bot erstellen");
  });

  it("activates the clicked button and toggles submenu correctly", async () => {
    const wrapper = mount(Sidebar);

    const buttons = wrapper.findAllComponents(SidebarButton);

    await buttons[0].trigger("click");
    expect(buttons[0].props("active")).toBe(true);
    expect(wrapper.text()).toContain("Aktive Bots");

    await buttons[0].trigger("click");
    expect(wrapper.text()).not.toContain("Aktive Bots");
  });
  it("navigates to submenu route on submenu button click", async () => {
    const mockPush = vi.fn();
    const wrapper = mount(Sidebar, {
      global: {
        mocks: {
          $router: { push: mockPush },
        },
      },
    });

    const buttons = wrapper.findAllComponents(SidebarButton);
    await buttons[0].trigger("click");

    const submenuButtons = wrapper.findAll('button');
    const submenuBtn = submenuButtons.find(btn => btn.text().includes("Aktive Bots"));
    expect(submenuBtn).toBeDefined();

    await submenuBtn!.trigger("click");
    expect(mockPush).toHaveBeenCalledWith("/");
  });
  it('sets openedSubmenuIndex to null when clicking a button without submenu', async () => {
  const wrapper = mount(Sidebar)

  const buttons = wrapper.findAllComponents(SidebarButton)

  // Сначала откроем подменю первой кнопки (которая с submenu)
  await buttons[0].trigger('click')
  expect(wrapper.vm.openedSubmenuIndex).toBe(0)

  // Затем кликнем по кнопке без подменю
  await buttons[1].trigger('click')

  // Проверим, что подменю закрылось
  expect(wrapper.vm.openedSubmenuIndex).toBe(null)

  // И активный индекс тоже сменился
  expect(wrapper.vm.activeIndex).toBe(1)
})
});
