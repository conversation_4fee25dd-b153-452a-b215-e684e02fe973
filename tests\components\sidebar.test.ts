import { mount } from "@vue/test-utils";
import { describe, it, expect, vi } from "vitest";
import Sidebar from "../../components/Sidebar.vue";
import SidebarButton from "../../components/SidebarButton.vue";

vi.mock("@/components/SidebarButton.vue", () => ({
  default: {
    name: "SidebarButton",
    props: ["text", "icon", "active"],
    emits: ["click"],
    template: `<button @click="$emit('click')">{{ text }}</button>`,
  },
}));

describe("Sidebar.vue", () => {
  it("renders all buttons", () => {
    const wrapper = mount(Sidebar);
    const buttons = wrapper.findAllComponents(SidebarButton);
    expect(buttons).toHaveLength(2);
    expect(buttons[0].text()).toContain("Bot-Dashboard");
    expect(buttons[1].text()).toContain("Bot erstellen");
  });

  it("activates the clicked button and toggles submenu correctly", async () => {
    const wrapper = mount(Sidebar);

    const buttons = wrapper.findAllComponents(SidebarButton);

    await buttons[0].trigger("click");
    expect(buttons[0].props('active')).toBe(true);
    expect(wrapper.find('button:contains("Aktive Bots")').exists()).toBe(true);

    // Click again to close submenu
    await buttons[0].trigger("click");
    expect(wrapper.find('button:contains("Aktive Bots")').exists()).toBe(false);
  });
});
