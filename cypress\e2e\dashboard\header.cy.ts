describe('Header Component', () => {
  beforeEach(() => {
    cy.visit('/');
  });
    it('renders the <header> element with correct classes', () => {
    cy.get('header')
      .should('exist')
      .and('have.class', 'bg-base-100')
      .and('have.class', 'px-6')
      .and('have.class', 'py-4')
      .and('have.class', 'shadow-sm')
      .and('have.class', 'flex')
      .and('have.class', 'justify-between')
      .and('have.class', 'items-center');
  });

  it('renders the company logo correctly', () => {
    cy.get('header img[alt="Logo"]')
      .should('exist')
      .and('have.attr', 'src', '/oracom-intelligence-logo.svg')
      .and('have.class', 'h-12');
  });

  it('renders the user avatar', () => {
    cy.get('header img[alt="User Avatar"]')
      .should('exist')
      .and('have.attr', 'src', '/user-avatar.svg')
      .and('have.class', 'rounded-full');
  });
});
