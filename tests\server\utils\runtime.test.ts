import { describe, it, expect, vi } from 'vitest'

vi.mock('@supabase/supabase-js', () => ({
  createClient: vi.fn(() => ({
    from: vi.fn(),
  })),
}));

vi.stubGlobal('useRuntimeConfig', () => ({
  private: {
    supabaseUrl: 'https://example.supabase.co',
    supabaseKey: 'test-key-123',
  },
}));

import { getConfig } from '../../../server/utils/runtime';

describe('getConfig', () => {
  beforeEach(() => {
    vi.resetModules();
    vi.clearAllMocks();
  });

  it('returns the mocked private config', () => {
    const config = getConfig();
    expect(config).toEqual({
      supabaseUrl: 'https://example.supabase.co',
      supabaseKey: 'test-key-123'
    });
  });
});