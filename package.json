{"name": "oracom-platform", "private": true, "type": "module", "scripts": {"start-server": "nuxt start", "build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "test": "vitest", "test:watch": "vitest --watch", "test:changed": "vitest --watch --changed", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "cy:open": "cypress open", "cy:cli": "cypress run", "test:e2e": "start-server-and-test start-server http://localhost:3000 cy:cli", "prepare": "husky"}, "dependencies": {"@supabase/supabase-js": "^2.50.0", "daisyui": "^5.0.43", "nuxt": "^3.17.5", "tailwindcss": "^4.1.10", "vue": "^3.5.16", "vue-router": "^4.5.1"}, "devDependencies": {"@nuxt/test-utils": "^3.19.1", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/vite": "^4.1.10", "@vitejs/plugin-vue": "^5.2.4", "@vitest/coverage-v8": "^3.2.3", "@vitest/ui": "^3.2.3", "@vue/test-utils": "^2.4.6", "cypress": "^14.4.1", "cypress-vite": "^1.6.0", "happy-dom": "^17.6.3", "husky": "^9.1.7", "start-server-and-test": "^2.0.12", "unplugin-auto-import": "^19.3.0", "vitest": "^3.2.3"}}