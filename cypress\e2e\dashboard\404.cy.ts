// Describe a user story for the flow: A user can't find the page: 404
// Example:
// As a user I land on the wrong page - a 404 page is displayed
// I press on the go-back-link
// I land on the start page
// etc.
// ***********************************************************

describe('404 flow', () => {
  beforeEach(() => {
    cy.visit('/wrong-url', {failOnStatusCode: false});
  });

  it('should display a button', () => {
    cy.get('h1').should('contain', 'Ein Fehler ist passiert.');
    cy.get('[data-cy="goBackLink"]')
      .should('be.visible')
      .should('contain', 'Zurück zur Startseite')
      .click();
    cy.url().should('include', '/');
  });
});