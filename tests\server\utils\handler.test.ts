import { describe, it, expect, vi, beforeAll, afterAll } from 'vitest'
import h3EventMock from '../../mocks/h3EventMock';
import { defineWrappedResponseHandler } from '../../../server/utils/handler'

describe('defineWrappedResponseHandler', () => {
  beforeAll(() => {
    // @ts-ignore
    globalThis.defineEventHandler = (fn: any) => fn
  });

  afterAll(() => {
    // @ts-ignore
    delete globalThis.defineEventHandler
  });

  it('returns success with data when handler resolves', async () => {
    const data = { hello: 'world' }

    const handler = defineWrappedResponseHandler(async () => data)

    const result = await handler(h3EventMock)

    expect(result).toMatchObject({
      success: true,
      data: { hello: 'world' },
    });
  });

  it('returns error object when handler throws', async () => {
    const errorMessage = 'Kaboom!'

    const handler = defineWrappedResponseHandler(async () => {
      throw new Error(errorMessage)
    })

    const result = await handler(h3EventMock)

    expect(result.success).toBe(false)
    if (!result.success) {
      expect(result.error).toMatchObject({
        message: errorMessage,
      })

      if (process.env.NODE_ENV === 'development') {
        expect(result.error.stack).toBeDefined()
      } else {
        expect(result.error.stack).toBeUndefined()
      }
    }
  });

  it('returns "Unknown error" when error has no message', async () => {
    const handler = defineWrappedResponseHandler(async () => {
      const error = new Error();
      (error as any).message = undefined;
      throw error;
    });

    const result = await handler(h3EventMock);

    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.message).toBe('Unknown error');
    }
  });

  it('includes error stack trace in development environment', async () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    const handler = defineWrappedResponseHandler(async () => {
      throw new Error('Test error');
    });

    const result = await handler(h3EventMock);

    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.stack).toBeDefined();
    }

    process.env.NODE_ENV = originalEnv;
  });

  it('excludes error stack trace in production environment', async () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';

    const handler = defineWrappedResponseHandler(async () => {
      throw new Error('Test error');
    });

    const result = await handler(h3EventMock);

    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.stack).toBeUndefined();
    }

    process.env.NODE_ENV = originalEnv;
  });
});
