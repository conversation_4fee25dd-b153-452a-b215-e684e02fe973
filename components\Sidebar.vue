<script setup lang="ts">
import { ref } from "vue";

interface SubmenuItem {
  text: string;
  route: string;
}

interface ButtonItem {
  text: string;
  icon: string;
  submenu?: SubmenuItem[];
  route?: string;
}

const buttons: ButtonItem[] = [
  {
    text: "Bot-Dashboard",
    icon: "/group-for-btn.svg",
    submenu: [{ text: "Aktive Bots", route: "/aktive-bots" }],
  },
  { text: "Bot erstellen", icon: "/shines.svg", route: "/bot-erstellen" },
];

const activeButton = ref(buttons[0].text);

const openStates = ref<Record<string, boolean>>({});

function handleClick(btn: ButtonItem) {
  activeButton.value = btn.text;
  if (btn.submenu) {
    const isOpen = openStates.value[btn.text];
    openStates.value = {};
    if (!isOpen) {
      openStates.value[btn.text] = true;
    }
  } else {
    openStates.value = {};
  }
}
</script>

<template>
  <aside class="bg-base-100 p-4 h-full text-sm">
    <ul class="menu bg-base-100 p-2">
      <li v-for="(btn, index) in buttons" :key="index">
        <details v-if="btn.submenu" class="group" :open="!!openStates[btn.text]">
          <summary @click.prevent="handleClick(btn)" :class="[
            'font-semibold px-4 py-2 cursor-pointer',
            activeButton === btn.text
              ? 'bg-primary text-primary-content oracom-shadow'
              : 'text-neutral-content brightness-50',
          ]">
            <div class="flex items-center gap-2">
              <img :src="btn.icon" :class="['w-5 h-5', activeButton === btn.text]" alt="icon" />
              {{ btn.text }}
            </div>
          </summary>
          <ul class="bg-base-100 mt-2 ml-2">
            <li v-for="(item, idx) in btn.submenu" :key="`submenu-${index}-${idx}`">
              <a :href="item.route" class="text-sm font-medium text-neutral-content brightness-50">
                {{ item.text }}
              </a>
            </li>
          </ul>
        </details>

        <a v-else :href="btn.route || '#'" @click.prevent="handleClick(btn)" :class="[
          'flex items-center gap-2 px-4 py-2 font-semibold  cursor-pointer',
          activeButton === btn.text
            ? 'bg-primary text-primary-content oracom-shadow'
            : 'text-neutral-content brightness-50',
        ]">
          <img :src="btn.icon" :class="['w-5 h-5', activeButton === btn.text]" alt="icon" />
          {{ btn.text }}
        </a>
      </li>
    </ul>
  </aside>
</template>

<style scoped></style>
