<script setup lang="ts">
import { ref } from "vue";
import SidebarButton from "../components/SidebarButton.vue";

const buttons = [
  {
    text: "Bot-Dashboard",
    icon: "/group-for-btn.svg",
    submenu: [{ text: "Aktive Bots", route: "/" }],
  },
  { text: "Bot erstellen", icon: "/shines.svg" },
];

const activeIndex = ref<number | null>(0);
const openedSubmenuIndex = ref<number | null>(null);

const setActive = (index: number) => {
  if (buttons[index].submenu) {
    openedSubmenuIndex.value =
      openedSubmenuIndex.value === index ? null : index;
  } else {
    openedSubmenuIndex.value = null;
  }
  activeIndex.value = index;
};
</script>

<template>
  <aside class="bg-base-100 p-4 h-full text-sm space-y-4">
    <div v-for="(btn, index) in buttons" :key="btn.text">
      <SidebarButton
        :text="btn.text"
        :icon="btn.icon"
        :active="activeIndex === index"
        @click="setActive(index)"
      />
      <div
        v-if="openedSubmenuIndex === index && btn.submenu"
        class="ml-8 mt-1 flex flex-col gap-1"
      >
        <button
          v-for="sub in btn.submenu"
          :key="sub.text"
          class="text-left text-xs text-neutral hover:text-primary cursor-pointer"
          @click="$router.push(sub.route)"
        >
          {{ sub.text }}
        </button>
      </div>
    </div>
  </aside>
</template>

<style scoped></style>
