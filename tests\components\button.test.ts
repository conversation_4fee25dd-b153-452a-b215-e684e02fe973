import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import Button from '../../components/Button.vue';

const navigateToMock = vi.fn();
vi.stubGlobal('navigateTo', navigateToMock);

describe('pages/index.vue', () => {
  beforeEach(() => {
    navigateToMock.mockClear();
  });

  it('renders the text', () => {
    const wrapper = mount(Button);
    expect(wrapper.text()).toContain('Click me!');
  });

  it('calls navigateTo with the correct path on button click', async () => {
    const wrapper = mount(Button);
    const button = wrapper.get('[data-cy="myButton"]');
    await button.trigger('click');
    expect(navigateToMock).toHaveBeenCalledWith('/andereSeite');
  });
});