import { describe, it, expect } from "vitest";
import { mount } from "@vue/test-utils";
import Default from "../../layouts/default.vue";

describe("default layout", () => {
  const wrapper = mount(Default);
  it('should render a div with name="layout-container" and expected class', () => {
    const layoutDiv = wrapper.find('div[name="layout-container"]');

    expect(layoutDiv.exists()).toBe(true);
    expect(layoutDiv.attributes("class")).toContain("flex");
    expect(layoutDiv.attributes("class")).toContain("flex-col");
    expect(layoutDiv.attributes("class")).toContain("h-screen");
  });
  it('should render a div with name="sidebar-container" and expected class', () => {
    const layoutDiv = wrapper.find('div[name="body-container"]');

    expect(layoutDiv.exists()).toBe(true);
    expect(layoutDiv.attributes("class")).toContain("flex");
    expect(layoutDiv.attributes("class")).toContain("flex-1");
    expect(layoutDiv.attributes("class")).toContain("overflow-hidden");
  });
  it('should render a div with name="content-container" and expected class', () => {
    const layoutDiv = wrapper.find('main[name="main-container"]');

    expect(layoutDiv.exists()).toBe(true);
    expect(layoutDiv.attributes("class")).toContain("flex-1");
    expect(layoutDiv.attributes("class")).toContain("p-6");
    expect(layoutDiv.attributes("class")).toContain("overflow-y-auto");
  });

  it('renders content in "header" slot', () => {
    const wrapper = mount(Default, {
      slots: {
        header: '<div id="test-header-slot">Header Slot Content</div>',
      },
    });
    expect(wrapper.find("#test-header-slot").exists()).toBe(true);
  });

  it('renders content in "sidebar" slot', () => {
    const wrapper = mount(Default, {
      slots: {
        sidebar: '<div id="test-sidebar-slot">Sidebar Slot Content</div>',
      },
    });
    expect(wrapper.find("#test-sidebar-slot").exists()).toBe(true);
  });

});
